# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from __future__ import annotations

import torch
from typing import TYPE_CHECKING

from isaaclab.assets import Articulation, RigidObject
from isaaclab.managers import SceneEntityCfg
from isaaclab.sensors import FrameTransformer
from isaaclab.utils.math import combine_frame_transforms, euler_xyz_from_quat, quat_mul, quat_conjugate

from loguru import logger


if TYPE_CHECKING:
    from isaaclab.envs import ManagerBasedRLEnv


def object_is_lifted(
    env: ManagerBasedRLEnv, minimal_height: float, object_cfg: SceneEntityCfg = SceneEntityCfg("object")
) -> torch.Tensor:
    """Reward the agent for lifting the object above the minimal height."""
    object: RigidObject = env.scene[object_cfg.name]
    return torch.where(object.data.root_pos_w[:, 2] > minimal_height, 1.0, 0.0)


def object_ee_distance(
    env: ManagerBasedRLEnv,
    std: float,
    object_cfg: SceneEntityCfg = SceneEntityCfg("object"),
    ee_frame_cfg: SceneEntityCfg = SceneEntityCfg("ee_frame"),
) -> torch.Tensor:
    """Reward the agent for reaching the object using tanh-kernel."""
    # extract the used quantities (to enable type-hinting)
    object: RigidObject = env.scene[object_cfg.name]
    ee_frame: FrameTransformer = env.scene[ee_frame_cfg.name]
    # Target object position: (num_envs, 3)
    cube_pos_w = object.data.root_pos_w
    # End-effector position: (num_envs, 3)
    ee_w = ee_frame.data.target_pos_w[..., 0, :]
    # Distance of the end-effector to the object: (num_envs,)
    object_ee_distance = torch.norm(cube_pos_w - ee_w, dim=1)

    # Print end-effector pose information for the first environment
    # if env.num_envs > 0:
    #     logger.info(f"Object-EE distance: {object_ee_distance[0].cpu().numpy():.4f}")

    return 1 - torch.tanh(object_ee_distance / std)


def object_goal_distance(
    env: ManagerBasedRLEnv,
    std: float,
    minimal_height: float,
    command_name: str,
    robot_cfg: SceneEntityCfg = SceneEntityCfg("robot"),
    object_cfg: SceneEntityCfg = SceneEntityCfg("object"),
) -> torch.Tensor:
    """Reward the agent for tracking the goal pose using tanh-kernel."""
    # extract the used quantities (to enable type-hinting)
    robot: RigidObject = env.scene[robot_cfg.name]
    object: RigidObject = env.scene[object_cfg.name]
    command = env.command_manager.get_command(command_name)
    # compute the desired position in the world frame
    des_pos_b = command[:, :3]
    des_pos_w, _ = combine_frame_transforms(robot.data.root_pos_w, robot.data.root_quat_w, des_pos_b)
    # distance of the end-effector to the object: (num_envs,)
    distance = torch.norm(des_pos_w - object.data.root_pos_w, dim=1)

    # logger.info(f"joint_pos: {robot.data.joint_pos[:, :]}")

    # rewarded if the object is lifted above the threshold
    return (object.data.root_pos_w[:, 2] > minimal_height) * (1 - torch.tanh(distance / std))


def gripper_close_when_near_object(
    env: ManagerBasedRLEnv,
    distance_threshold: float,
    asset_cfg: SceneEntityCfg,
    object_cfg: SceneEntityCfg = SceneEntityCfg("object"),
    ee_frame_cfg: SceneEntityCfg = SceneEntityCfg("ee_frame"),
) -> torch.Tensor:
    """Reward for closing gripper when near the object."""
    # extract the used quantities
    robot: Articulation = env.scene[asset_cfg.name]
    object: RigidObject = env.scene[object_cfg.name]
    ee_frame: FrameTransformer = env.scene[ee_frame_cfg.name]

    # Get gripper joint positions
    gripper_joint_pos = robot.data.joint_pos[:, asset_cfg.joint_ids]

    # Calculate distance between end-effector and object
    object_pos = object.data.root_pos_w
    ee_pos = ee_frame.data.target_pos_w[..., 0, :]
    distance = torch.norm(object_pos - ee_pos, dim=1)

    # Only reward when close to object
    is_near = distance < distance_threshold

    # Reward for closing gripper (assuming positive values = closed)
    # For ZLZK gripper: positive values close the gripper
    gripper_closure = torch.mean(gripper_joint_pos, dim=1)  # Average of both gripper joints

    # Normalize gripper closure to [0, 1] range (assuming max closure is around 0.5)
    gripper_closure_normalized = torch.clamp(gripper_closure / 0.5, 0.0, 1.0)

    return is_near.float() * gripper_closure_normalized


def object_position_error(
    env: ManagerBasedRLEnv,
    command_name: str,
    robot_cfg: SceneEntityCfg = SceneEntityCfg("robot"),
    object_cfg: SceneEntityCfg = SceneEntityCfg("object"),
) -> torch.Tensor:
    """Reward the agent for tracking the goal pose using tanh-kernel."""
    # extract the used quantities (to enable type-hinting)
    robot: RigidObject = env.scene[robot_cfg.name]
    object: RigidObject = env.scene[object_cfg.name]
    command = env.command_manager.get_command(command_name)
    # compute the desired position in the world frame
    des_pos_b = command[:, :3]
    des_pos_w, _ = combine_frame_transforms(robot.data.root_pos_w, robot.data.root_quat_w, des_pos_b)
    # distance of the end-effector to the object: (num_envs,)
    distance = torch.norm(des_pos_w - object.data.root_pos_w, dim=1)
    return distance


def ee_oriantation_error(
    env: ManagerBasedRLEnv,
    target_rp_quat: torch.Tensor,
    ee_frame_cfg: SceneEntityCfg = SceneEntityCfg("ee_frame"),
) -> torch.Tensor:
    """
    计算末端执行器姿态误差，对yaw角偏差不敏感，对roll和pitch角偏差敏感。

    Args:
        env: 环境实例
        target_rp_quat: 目标姿态四元数 (w, x, y, z)，形状为 (num_envs, 4)
        ee_frame_cfg: 末端执行器frame配置

    Returns:
        姿态误差值，形状为 (num_envs,)。yaw偏差时接近0，roll/pitch偏差时随偏差增加而增加。
    """
    # 获取末端执行器当前姿态
    ee_frame: FrameTransformer = env.scene[ee_frame_cfg.name]
    ee_quat_w = ee_frame.data.target_quat_w[..., 0, :]  # (num_envs, 4)

    # 确保target_rp_quat的形状正确
    if target_rp_quat.dim() == 1:
        target_rp_quat = target_rp_quat.unsqueeze(0).expand(env.num_envs, -1)

    # 计算相对四元数：q_rel = q_target^(-1) * q_current
    # 这里使用四元数乘法：q_rel = quat_conjugate(target) * current
    target_conj = quat_conjugate(target_rp_quat)
    q_rel = quat_mul(target_conj, ee_quat_w)

    # 将相对四元数转换为欧拉角以分离roll, pitch, yaw
    roll_error, pitch_error, _ = euler_xyz_from_quat(q_rel)

    # 计算roll和pitch的误差幅度（忽略yaw）
    # 使用平方和来计算roll和pitch的总误差
    rp_error_magnitude = torch.sqrt(roll_error**2 + pitch_error**2)

    # 可选：添加调试信息
    if env.num_envs > 0:
        logger.info(f"Roll error: {roll_error[0]:.4f}, Pitch error: {pitch_error[0]:.4f}, Yaw error: {yaw_error[0]:.4f}")
        logger.info(f"RP error magnitude: {rp_error_magnitude[0]:.4f}")

    return rp_error_magnitude
